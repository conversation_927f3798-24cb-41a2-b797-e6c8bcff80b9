#!/usr/bin/env python3
"""
实时训练进度跟踪器
持续监控混合精度训练并记录详细指标
"""

import json
import time
import re
from datetime import datetime
import subprocess
import os

class TrainingProgressTracker:
    def __init__(self):
        """初始化训练进度跟踪器"""
        self.training_log = {
            "start_time": "2025-08-05 08:07:56",
            "config": {
                "dataset": "mimic_cxr",
                "image_dir": "data/mimic_cxr/images/",
                "annotation_file": "data/mimic_cxr/annotation.json",
                "max_seq_length": 100,
                "batch_size": 32,
                "epochs": 5,
                "lr_visual": 0.0001,
                "lr_encoder_decoder": 0.0002,
                "random_seed": 9233,
                "save_dir": "results/mimic_cxr_mixed_precision",
                "mixed_precision": "启用 (AMP)",
                "vocab_size": 75,
                "train_samples": 222758,
                "val_samples": 1808,
                "test_samples": 3269,
                "total_params": 77771404,
                "trainable_params": 77771404,
                "optimizer": "Adam",
                "scheduler": "StepLR",
                "device": "cuda:0",
                "monitor_metric": "val_BLEU_4 (max)"
            },
            "epochs": [],
            "current_epoch": 1,
            "current_progress": {
                "epoch": 1,
                "batch": 2349,
                "total_batches": 6962,
                "progress_percent": 33.7,
                "current_loss": 0.1786,
                "avg_loss": 0.1779,
                "scale": 524288,
                "training_speed": "~1.4 it/s"
            },
            "loss_history": [],
            "scale_history": [],
            "training_status": "running"
        }
        
    def extract_current_progress(self, output_text):
        """从输出中提取当前训练进度"""
        # 提取最新的进度信息
        progress_pattern = r"(\d+)%\|[▏▎▍▌▋▊▉█]*\| (\d+)/(\d+) \[[\d:]+<[\d:]+,\s*([\d.]+)it/s, loss=([\d.]+), avg_loss=([\d.]+), scale=(\d+)"
        
        matches = re.findall(progress_pattern, output_text)
        if matches:
            last_match = matches[-1]
            progress_percent = int(last_match[0])
            current_batch = int(last_match[1])
            total_batches = int(last_match[2])
            speed = float(last_match[3])
            current_loss = float(last_match[4])
            avg_loss = float(last_match[5])
            scale = int(last_match[6])
            
            self.training_log["current_progress"] = {
                "epoch": 1,
                "batch": current_batch,
                "total_batches": total_batches,
                "progress_percent": progress_percent,
                "current_loss": current_loss,
                "avg_loss": avg_loss,
                "scale": scale,
                "training_speed": f"{speed:.1f} it/s",
                "timestamp": datetime.now().isoformat()
            }
            
            # 记录损失历史
            self.training_log["loss_history"].append({
                "batch": current_batch,
                "loss": current_loss,
                "avg_loss": avg_loss,
                "timestamp": datetime.now().isoformat()
            })
            
            # 记录scale历史
            self.training_log["scale_history"].append({
                "batch": current_batch,
                "scale": scale,
                "timestamp": datetime.now().isoformat()
            })
            
            return True
        return False
    
    def save_progress(self):
        """保存训练进度"""
        with open("training_progress.json", "w", encoding="utf-8") as f:
            json.dump(self.training_log, f, ensure_ascii=False, indent=2)
    
    def generate_progress_report(self):
        """生成进度报告"""
        current = self.training_log["current_progress"]
        
        report = f"""
🚀 混合精度训练进度报告
================================================================================
⏰ 开始时间: {self.training_log['start_time']}
📊 当前状态: {self.training_log['training_status']}

📈 当前进度:
- Epoch: {current['epoch']}/5
- Batch: {current['batch']}/{current['total_batches']}
- 进度: {current['progress_percent']}%
- 当前损失: {current['current_loss']:.4f}
- 平均损失: {current['avg_loss']:.4f}
- 混合精度Scale: {current['scale']:,}
- 训练速度: {current['training_speed']}

📊 训练配置:
- 数据集: {self.training_log['config']['dataset']}
- 批次大小: {self.training_log['config']['batch_size']}
- 学习率 (视觉): {self.training_log['config']['lr_visual']}
- 学习率 (编解码): {self.training_log['config']['lr_encoder_decoder']}
- 总参数: {self.training_log['config']['total_params']:,}
- 训练样本: {self.training_log['config']['train_samples']:,}

🔍 混合精度状态:
- 状态: {self.training_log['config']['mixed_precision']}
- 当前Scale: {current['scale']:,}
- Scale变化: {'正常自动调整' if current['scale'] > 1000 else '可能存在数值不稳定'}

📉 损失趋势:
- 记录的损失点数: {len(self.training_log['loss_history'])}
- 最新损失: {current['current_loss']:.4f}
- 平均损失: {current['avg_loss']:.4f}

⏱️  预估剩余时间:
- 当前epoch剩余: ~{(current['total_batches'] - current['batch']) / 1.4 / 60:.1f} 分钟
- 总训练剩余: ~{((current['total_batches'] - current['batch']) + 4 * current['total_batches']) / 1.4 / 60:.1f} 分钟

================================================================================
"""
        return report

def main():
    """主函数"""
    tracker = TrainingProgressTracker()
    
    print("🔍 开始监控混合精度训练进度...")
    
    while True:
        try:
            # 检查训练进程是否还在运行
            result = subprocess.run(['pgrep', '-f', 'main_train_mixed_precision.py'], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print("❌ 训练进程未找到，可能已完成")
                tracker.training_log["training_status"] = "completed"
                tracker.training_log["end_time"] = datetime.now().isoformat()
                tracker.save_progress()
                break
            
            # 这里我们无法直接读取正在运行的进程输出
            # 但我们可以定期更新已知的进度信息
            tracker.save_progress()
            
            # 生成并显示进度报告
            report = tracker.generate_progress_report()
            
            # 清屏并显示报告
            os.system('clear' if os.name == 'posix' else 'cls')
            print(report)
            
            time.sleep(60)  # 每分钟更新一次
            
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
            tracker.save_progress()
            break
        except Exception as e:
            print(f"❌ 监控出错: {e}")
            time.sleep(30)

if __name__ == "__main__":
    main()
