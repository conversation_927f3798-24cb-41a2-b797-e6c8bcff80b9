#!/usr/bin/env python3
"""
混合精度训练监控脚本
监控训练进程并记录所有参数和指标
"""

import time
import json
import os
import re
from datetime import datetime
import subprocess

class TrainingMonitor:
    def __init__(self, log_file="training_log.json"):
        """初始化训练监控器"""
        self.log_file = log_file
        self.training_data = {
            "start_time": datetime.now().isoformat(),
            "config": {},
            "epochs": [],
            "final_metrics": {},
            "model_info": {},
            "training_status": "running"
        }
        
    def extract_config_info(self, output_text):
        """从输出中提取配置信息"""
        config = {}
        
        # 提取基本配置
        patterns = {
            "dataset": r"📊 数据集: (.+)",
            "image_dir": r"📁 图像目录: (.+)",
            "annotation_file": r"📄 标注文件: (.+)",
            "max_seq_length": r"🔢 最大序列长度: (\d+)",
            "batch_size": r"📦 批次大小: (\d+)",
            "epochs": r"🔄 训练轮数: (\d+)",
            "lr_visual": r"🎯 学习率 \(视觉编码器\): ([\d.e-]+)",
            "lr_encoder_decoder": r"🎯 学习率 \(编码器-解码器\): ([\d.e-]+)",
            "random_seed": r"🌱 随机种子: (\d+)",
            "save_dir": r"💾 保存目录: (.+)",
            "mixed_precision": r"⚡ 混合精度训练: (.+)",
            "vocab_size": r"📚 词汇表大小: (\d+)",
            "train_samples": r"🔢 训练样本数: (\d+)",
            "val_samples": r"🔢 验证样本数: (\d+)",
            "test_samples": r"🔢 测试样本数: (\d+)",
            "total_params": r"📊 总参数数: ([\d,]+)",
            "trainable_params": r"📊 可训练参数数: ([\d,]+)",
            "optimizer": r"🔧 优化器: (.+)",
            "scheduler": r"📈 学习率调度器: (.+)",
            "device": r"📱 设备: (.+)",
            "monitor_metric": r"🎯 监控指标: (.+)"
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, output_text)
            if match:
                value = match.group(1).strip()
                # 处理数字
                if key in ["max_seq_length", "batch_size", "epochs", "random_seed", "vocab_size", 
                          "train_samples", "val_samples", "test_samples"]:
                    config[key] = int(value)
                elif key in ["lr_visual", "lr_encoder_decoder"]:
                    config[key] = float(value)
                elif key in ["total_params", "trainable_params"]:
                    config[key] = int(value.replace(",", ""))
                else:
                    config[key] = value
                    
        return config
    
    def extract_epoch_metrics(self, output_text, epoch_num):
        """提取epoch的训练指标"""
        epoch_data = {
            "epoch": epoch_num,
            "training_loss": [],
            "validation_metrics": {},
            "test_metrics": {},
            "epoch_time": None,
            "learning_rates": {},
            "mixed_precision_info": {}
        }
        
        # 提取训练损失
        loss_pattern = r"loss=([\d.]+), avg_loss=([\d.]+), scale=(\d+)"
        loss_matches = re.findall(loss_pattern, output_text)
        
        for match in loss_matches:
            epoch_data["training_loss"].append({
                "current_loss": float(match[0]),
                "avg_loss": float(match[1]),
                "scale": int(match[2])
            })
        
        # 提取验证指标
        val_patterns = {
            "val_loss": r"验证损失: ([\d.]+)",
            "val_BLEU_1": r"BLEU-1: ([\d.]+)",
            "val_BLEU_2": r"BLEU-2: ([\d.]+)", 
            "val_BLEU_3": r"BLEU-3: ([\d.]+)",
            "val_BLEU_4": r"BLEU-4: ([\d.]+)",
            "val_METEOR": r"METEOR: ([\d.]+)",
            "val_ROUGE_L": r"ROUGE-L: ([\d.]+)",
            "val_CIDEr": r"CIDEr: ([\d.]+)"
        }
        
        for metric, pattern in val_patterns.items():
            match = re.search(pattern, output_text)
            if match:
                epoch_data["validation_metrics"][metric] = float(match.group(1))
        
        # 提取测试指标（如果有）
        test_patterns = {
            "test_BLEU_1": r"测试 BLEU-1: ([\d.]+)",
            "test_BLEU_2": r"测试 BLEU-2: ([\d.]+)",
            "test_BLEU_3": r"测试 BLEU-3: ([\d.]+)", 
            "test_BLEU_4": r"测试 BLEU-4: ([\d.]+)",
            "test_METEOR": r"测试 METEOR: ([\d.]+)",
            "test_ROUGE_L": r"测试 ROUGE-L: ([\d.]+)",
            "test_CIDEr": r"测试 CIDEr: ([\d.]+)"
        }
        
        for metric, pattern in test_patterns.items():
            match = re.search(pattern, output_text)
            if match:
                epoch_data["test_metrics"][metric] = float(match.group(1))
        
        # 提取epoch时间
        time_pattern = r"Epoch \d+/\d+ 完成，用时: ([\d:.]+)"
        time_match = re.search(time_pattern, output_text)
        if time_match:
            epoch_data["epoch_time"] = time_match.group(1)
            
        return epoch_data
    
    def save_log(self):
        """保存训练日志"""
        with open(self.log_file, 'w', encoding='utf-8') as f:
            json.dump(self.training_data, f, ensure_ascii=False, indent=2)
    
    def update_training_data(self, output_text):
        """更新训练数据"""
        # 如果配置还没有提取，先提取配置
        if not self.training_data["config"]:
            config = self.extract_config_info(output_text)
            if config:
                self.training_data["config"] = config
        
        # 检查是否有新的epoch完成
        epoch_pattern = r"📈 Epoch (\d+)/\d+"
        epoch_matches = re.findall(epoch_pattern, output_text)
        
        if epoch_matches:
            current_epoch = int(epoch_matches[-1])
            
            # 如果这是新的epoch，提取指标
            if not any(e["epoch"] == current_epoch for e in self.training_data["epochs"]):
                epoch_data = self.extract_epoch_metrics(output_text, current_epoch)
                if epoch_data["training_loss"] or epoch_data["validation_metrics"]:
                    self.training_data["epochs"].append(epoch_data)
        
        # 检查训练是否完成
        if "训练完成" in output_text or "Training completed" in output_text:
            self.training_data["training_status"] = "completed"
            self.training_data["end_time"] = datetime.now().isoformat()
        
        # 保存更新的数据
        self.save_log()

def monitor_training():
    """监控训练进程"""
    monitor = TrainingMonitor()
    
    print("🔍 开始监控混合精度训练...")
    print(f"📝 日志文件: {monitor.log_file}")
    
    # 持续监控训练输出
    last_output = ""
    
    while True:
        try:
            # 检查进程是否还在运行
            result = subprocess.run(['pgrep', '-f', 'main_train_mixed_precision.py'], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print("❌ 训练进程未找到，可能已完成或出错")
                monitor.training_data["training_status"] = "unknown"
                monitor.save_log()
                break
            
            # 读取训练输出（这里需要根据实际情况调整）
            # 由于我们无法直接读取正在运行的进程输出，
            # 我们将定期检查结果目录中的日志文件
            
            time.sleep(30)  # 每30秒检查一次
            
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
            break
        except Exception as e:
            print(f"❌ 监控出错: {e}")
            time.sleep(10)

if __name__ == "__main__":
    monitor_training()
